uniform float time;
varying vec3 vColor;

void main() {
    float distance = length(gl_PointCoord - vec2(0.5));
    
    if (distance > 0.5) {
        discard;
    }
    
    float alpha = 1.0 - distance * 2.0;
    alpha = pow(alpha, 2.0);
    
    // Add twinkling effect
    float twinkle = sin(time * 3.0 + gl_FragCoord.x * 0.01 + gl_FragCoord.y * 0.01) * 0.5 + 0.5;
    alpha *= 0.7 + 0.3 * twinkle;
    
    gl_FragColor = vec4(vColor, alpha);
}
