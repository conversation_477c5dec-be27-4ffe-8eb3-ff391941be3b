# ✨ Three.js Portfolio with Starlight Background

A stunning single-page portfolio built with React, Three.js, Tailwind CSS, GLSL shaders, and GSAP animations featuring an immersive starlight background.

## 🚀 Features

- **Immersive Starfield Background**: Custom Three.js scene with GLSL shaders creating a twinkling starlight effect
- **Smooth Animations**: GSAP-powered animations with scroll triggers and smooth transitions
- **Interactive Elements**: Custom cursor with hover effects and floating particles
- **Responsive Design**: Fully responsive layout built with Tailwind CSS
- **Modern Tech Stack**: React 19, Three.js, GSAP, and Vite for optimal performance
- **Glass Morphism UI**: Beautiful frosted glass effects and gradient backgrounds

## 🛠️ Technologies Used

- **React 19** - Modern React with hooks
- **Three.js** - 3D graphics and WebGL rendering
- **GLSL Shaders** - Custom vertex and fragment shaders for starfield effects
- **GSAP** - Professional-grade animations and scroll triggers
- **Tailwind CSS** - Utility-first CSS framework
- **Vite** - Fast build tool and development server

## 📦 Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd portfolio
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## 🎨 Customization

### Personal Information
Edit the following in `src/App.jsx`:
- Replace "Your Name" with your actual name
- Update the title and description
- Add your email and LinkedIn profile links
- Customize the skills array in the About section

### Starfield Configuration
Modify the starfield in `src/components/Starfield.jsx`:
- `starCount`: Number of stars (default: 2000)
- `starColors`: Array of star colors
- Animation speed and rotation values

### Colors and Styling
Update colors in `src/App.jsx`:
- Gradient colors for text and buttons
- Background colors and opacity values
- Hover effects and transitions

### Projects Section
Add your actual projects by replacing the placeholder project cards:
- Project images/screenshots
- Project descriptions
- Live demo and code repository links

## 🎯 Performance Tips

- The starfield uses instanced rendering for optimal performance
- Animations are hardware-accelerated using GSAP
- Three.js scenes are properly disposed on component unmount
- Responsive design ensures good performance on mobile devices

## 📱 Browser Support

- Modern browsers with WebGL support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 🚀 Deployment

Build for production:
```bash
npm run build
```

The built files will be in the `dist` directory, ready for deployment to any static hosting service.

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Feel free to fork this project and customize it for your own portfolio. If you make improvements, pull requests are welcome!

---

**Built with ❤️ and Three.js**
