import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Starfield from './components/Starfield';
import FloatingParticles from './components/FloatingParticles';
import CustomCursor from './components/CustomCursor';

gsap.registerPlugin(ScrollTrigger);

function App() {
  const heroRef = useRef(null);
  const nameRef = useRef(null);
  const titleRef = useRef(null);
  const descriptionRef = useRef(null);
  const ctaRef = useRef(null);

  useEffect(() => {
    const tl = gsap.timeline();

    // Initial state
    gsap.set([nameRef.current, titleRef.current, descriptionRef.current, ctaRef.current], {
      opacity: 0,
      y: 50
    });

    // Animation sequence
    tl.to(nameRef.current, {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: "power3.out"
    })
    .to(titleRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.5")
    .to(descriptionRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.3")
    .to(ctaRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.3");

    // Floating animation for the hero section
    gsap.to(heroRef.current, {
      y: -10,
      duration: 3,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Scroll-triggered animations
    const sections = document.querySelectorAll('section:not(.hero)');
    sections.forEach((section) => {
      gsap.fromTo(section,
        {
          opacity: 0,
          y: 100
        },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: section,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    // Parallax effect for starfield
    gsap.to('.starfield', {
      yPercent: -50,
      ease: "none",
      scrollTrigger: {
        trigger: document.body,
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };

  }, []);

  return (
    <div className="relative min-h-screen overflow-hidden cursor-none">
      <CustomCursor />
      <Starfield />
      <FloatingParticles />

      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 p-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="text-white font-bold text-xl">Portfolio</div>
          <div className="hidden md:flex space-x-8">
            <a href="#about" className="interactive text-white/80 hover:text-white transition-colors">About</a>
            <a href="#projects" className="interactive text-white/80 hover:text-white transition-colors">Projects</a>
            <a href="#contact" className="interactive text-white/80 hover:text-white transition-colors">Contact</a>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero relative z-10 min-h-screen flex items-center justify-center px-6">
        <div ref={heroRef} className="text-center max-w-4xl mx-auto">
          <h1
            ref={nameRef}
            className="text-6xl md:text-8xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent"
          >
            Your Name
          </h1>
          <h2
            ref={titleRef}
            className="text-2xl md:text-4xl text-white/90 mb-8 font-light"
          >
            Full Stack Developer & Creative Technologist
          </h2>
          <p
            ref={descriptionRef}
            className="text-lg md:text-xl text-white/70 mb-12 max-w-2xl mx-auto leading-relaxed"
          >
            I craft digital experiences that blend cutting-edge technology with beautiful design.
            Passionate about creating innovative solutions that make a difference.
          </p>
          <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="interactive px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full font-semibold hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
              View My Work
            </button>
            <button className="interactive px-8 py-4 border-2 border-white/30 text-white rounded-full font-semibold hover:bg-white/10 transition-all duration-300">
              Get In Touch
            </button>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="relative z-10 py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-16 text-center">About Me</h2>
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <p className="text-lg text-white/80 mb-6 leading-relaxed">
                With over 5 years of experience in web development, I specialize in creating
                immersive digital experiences using modern technologies like React, Three.js,
                and advanced animation libraries.
              </p>
              <p className="text-lg text-white/80 mb-8 leading-relaxed">
                I believe in the power of clean code, beautiful design, and seamless user experiences.
                Every project is an opportunity to push boundaries and create something extraordinary.
              </p>
              <div className="flex flex-wrap gap-3">
                {['React', 'Three.js', 'GSAP', 'Node.js', 'TypeScript', 'WebGL'].map((skill) => (
                  <span
                    key={skill}
                    className="px-4 py-2 bg-white/10 text-white rounded-full text-sm backdrop-blur-sm"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
            <div className="relative">
              <div className="w-80 h-80 mx-auto bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-full backdrop-blur-sm border border-white/10"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="relative z-10 py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-16 text-center">Featured Projects</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((project) => (
              <div
                key={project}
                className="group relative bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300 transform hover:scale-105"
              >
                <div className="h-48 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-lg mb-6"></div>
                <h3 className="text-xl font-semibold text-white mb-3">Project {project}</h3>
                <p className="text-white/70 mb-4">
                  A brief description of this amazing project and the technologies used to build it.
                </p>
                <div className="flex gap-2">
                  <button className="px-4 py-2 bg-blue-500/20 text-blue-300 rounded-lg text-sm hover:bg-blue-500/30 transition-colors">
                    Live Demo
                  </button>
                  <button className="px-4 py-2 bg-white/10 text-white rounded-lg text-sm hover:bg-white/20 transition-colors">
                    Code
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="relative z-10 py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-8">Let's Work Together</h2>
          <p className="text-xl text-white/80 mb-12">
            Ready to bring your ideas to life? Let's create something amazing together.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full font-semibold hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105"
            >
              Send Email
            </a>
            <a
              href="https://linkedin.com/in/yourprofile"
              className="px-8 py-4 border-2 border-white/30 text-white rounded-full font-semibold hover:bg-white/10 transition-all duration-300"
            >
              LinkedIn
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 py-8 px-6 border-t border-white/10">
        <div className="max-w-6xl mx-auto text-center">
          <p className="text-white/60">
            © 2024 Your Name. Crafted with passion and Three.js.
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;